<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 表头
const headers = [
  { title: '姓名', key: 'name', sortable: false },
  { title: '登录邮箱', key: 'email', sortable: false },
  { title: '角色', key: 'role', sortable: false },
  { title: '状态', key: 'status', sortable: false },
  { title: '最近登录', key: 'lastLogin', sortable: false },
  { title: '创建时间', key: 'createdAt', sortable: false, width: '150px' },
  { title: '操作', key: 'actions', sortable: false, align: 'center' as const, width: '80px' },
]

// 示例数据http://*************:3101/v1/subAccount
const users = ref([])

const getUserList = async () => {
  console.log('getUserList')

  const res: any = await $get('/v1/subAccounts', {
    page: 1,
    size: 1000,
  })

  const result = res.result

  users.value = result.rows

  console.log(result)
}

const search = ref('')
const page = ref(1)
const itemsPerPage = ref(10)
const selectedRows = ref<number[]>([])

const dialogVisible = ref(false)
const isEdit = ref(false)
const password = ref('')
const showPassword = ref(false)

// 批量分配角色对话框
const batchAssignRoleDialog = ref(false)
const selectedRole = ref('')

// 角色选项
const roleOptions = ref([])

const getRoleList = async () => {
  console.log('getRoleList')

  const res: any = await $get('/v1/roles', {
    page: 1,
    size: 1000,
  })

  const result = res.result

  // 过滤掉系统角色（管理员角色），只显示自定义角色
  const roleList = result.rows
    .filter((item: any) => item.type !== 'SYSTEM')
    .map((item: any) => ({
      label: item.name,
      value: item.id,
    }))

  roleOptions.value = roleList
}

onMounted(() => {
  console.log('onMounted')
  getRoleList()
  getUserList()
})

interface User {
  index: number
  name: string
  email: string
  roleId: string
  isActive: boolean
  lastLogin: string
  createdAt: string
  actions: string
  id?: number
}

const editUser = ref<User>({
  index: 0,
  name: '',
  email: '',
  roleId: '',
  isActive: true,
  lastLogin: '',
  createdAt: new Date().toISOString().slice(0, 10),
  actions: '',
})

// 搜索
const filteredUsers = computed(() => {
  let list = users.value
  if (search.value)
    list = list.filter(u => u.name.includes(search.value))

  return list
})

// 新增用户函数 - 明确处理新增逻辑
function addUser() {
  isEdit.value = false
  editUser.value = {
    index: users.value.length + 1,
    name: '',
    email: '',
    roleId: '',
    isActive: true,
    lastLogin: '',
    createdAt: new Date().toISOString().slice(0, 10),
    actions: '',
  }
  password.value = ''
  dialogVisible.value = true
}

// 编辑用户函数 - 明确处理编辑逻辑
function editUserFunc(user: User) {
  isEdit.value = true
  editUser.value = { ...user }
  password.value = ''
  console.log('编辑用户 isEdit:', isEdit.value)
  dialogVisible.value = true
}

// 保存用户
async function saveUser() {
  if (!editUser.value.name) {
    ElMessage.error('请输入用户名')

    return
  }

  if (!editUser.value.email) {
    ElMessage.error('请输入邮箱')

    return
  }

  if (!isEdit.value && !password.value) {
    ElMessage.error('请输入密码')

    return
  }
  if (isEdit.value) {
    let data = {}
    if (password.value) {
      data = {
        name: editUser.value.name,
        email: editUser.value.email,
        roleId: editUser.value.roleId,
        isActive: editUser.value.isActive,
        password: md5Hash(password.value),

      }
    }
    else {
      data = {
        name: editUser.value.name,
        email: editUser.value.email,
        roleId: editUser.value.roleId,
        isActive: editUser.value.isActive,
      }
    }
    console.log(data)

    const res = await $put(`/v1/subAccount/${editUser.value.id}`, data)

    console.log(res)
    if (res.msg === 'success') {
      getUserList()
      ElMessage.success('编辑成功')
    }
  }
  else {
    const res = await $post('/v1/subAccount', {
      name: editUser.value.name,
      email: editUser.value.email,
      roleId: editUser.value.roleId,
      isActive: editUser.value.isActive,
      password: md5Hash(password.value),
    })

    console.log(res)
    if (res.msg === 'success') {
      getUserList()
      ElMessage.success('添加成功')
    }
  }
  password.value = ''
  dialogVisible.value = false
}

// 删除用户
function deleteUser(user: User) {
  ElMessageBox.confirm(`确定删除用户 ${user.name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await $delete(`/v1/subAccount/${user.id}`)

    console.log(res)
    if (res.msg === 'success') {
      getUserList()
      ElMessage.success('删除成功')
    }
  })
}

// 批量删除
function batchDelete() {
  if (!selectedRows.value.length) {
    ElMessage.warning('请先选择用户')

    return
  }

  ElMessageBox.confirm('确定批量删除选中用户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    users.value = users.value.filter(u => !selectedRows.value.includes(u.index))
    selectedRows.value = []
    ElMessage.success('批量删除成功')
  })
}

// 批量启用
function batchEnable() {
  if (!selectedRows.value.length) {
    ElMessage.warning('请先选择用户')

    return
  }

  ElMessageBox.confirm('确定批量启用选中用户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    users.value.forEach(user => {
      if (selectedRows.value.includes(user.index))
        user.status = '启用'
    })
    ElMessage.success('批量启用成功')
  })
}

// 批量禁用
function batchDisable() {
  if (!selectedRows.value.length) {
    ElMessage.warning('请先选择用户')

    return
  }

  ElMessageBox.confirm('确定批量禁用选中用户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    users.value.forEach(user => {
      if (selectedRows.value.includes(user.index))
        user.status = '禁用'
    })
    ElMessage.success('批量禁用成功')
  })
}

// 打开批量分配角色对话框
function openBatchAssignRole() {
  if (!selectedRows.value.length) {
    ElMessage.warning('请先选择用户')

    return
  }
  selectedRole.value = ''
  batchAssignRoleDialog.value = true
}

// 保存批量分配角色
function saveBatchAssignRole() {
  if (!selectedRole.value) {
    ElMessage.error('请选择角色')

    return
  }

  users.value.forEach(user => {
    if (selectedRows.value.includes(user.index))
      user.role = selectedRole.value
  })

  ElMessage.success('角色分配成功')
  batchAssignRoleDialog.value = false
}

// 切换状态
async function toggleStatus(user: User) {
  const res = await $put(`/v1/subAccount/${user.id}`, {
    isActive: !user.isActive,
  })

  if (res.msg === 'success') {
    getUserList()
    ElMessage.success('状态切换成功')
  }
}

// 重置密码
function resetPassword(user: User) {
  ElMessageBox.confirm(`确定重置 ${user.name} 的密码吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 这里应该调用重置密码的API，此处仅做模拟
    ElMessage.success('密码重置成功，新密码已发送至用户邮箱')
  })
}
</script>

<template>
  <div>
    <VCard class="mb-6">
      <template #title>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center gap-4">
            <div>用户管理</div>
            <AppTextField
              v-model="search"
              placeholder="搜索用户"
              density="compact"
              hide-details
              style="min-inline-size: 150px;"
            />
          </div>
          <div class="d-flex gap-4">
            <!-- 批量操作按钮 -->

            <VBtn
              v-if="selectedRows.length"
              color="secondary"
            >
              操作
              <VMenu activator="parent">
                <VList>
                  <VListItem @click="batchEnable">
                    批量启用
                  </VListItem>
                  <VListItem @click="batchDisable">
                    批量禁用
                  </VListItem>
                  <VListItem @click="batchDelete">
                    批量删除
                  </VListItem>
                  <VListItem @click="openBatchAssignRole">
                    批量分配角色
                  </VListItem>
                </VList>
              </VMenu>
            </VBtn>
            <VBtn
              color="primary"
              @click="addUser"
            >
              + 添加用户
            </VBtn>
          </div>
        </div>
      </template>
      <VDivider />

      <!-- 用户表格 -->
      <VDataTable
        v-model="selectedRows"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="filteredUsers"
        :items-length="filteredUsers.length"
        show-select
        class="user-table"
        :no-data-text="t('NoData')"
      >
        <template #item.name="{ item }">
          <div class="nowrap-cell">
            {{ item.name }}
          </div>
        </template>
        <template #item.role="{ item }">
          <div class="nowrap-cell">
            {{ item.role.name }}
          </div>
        </template>
        <template #item.status="{ item }">
          <VChip
            :color="item.isActive ? 'success' : 'error'"
            size="small"
          >
            {{ item.isActive ? '启用' : '禁用' }}
          </VChip>
        </template>
        <template #item.lastLogin="{ item }">
          <div class="nowrap-cell">
            {{ item.lastLogin ? formatTime(item.lastLogin) : '--' }}
          </div>
        </template>
        <template #item.createdAt="{ item }">
          <div class="nowrap-cell">
            {{ formatTime(item.createdAt) }}
          </div>
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="editUserFunc(item)">
                  编辑
                </VListItem>
                <VListItem
                  v-if="item.isActive"
                  @click="toggleStatus(item)"
                >
                  禁用
                </VListItem>
                <VListItem
                  v-else
                  @click="toggleStatus(item)"
                >
                  启用
                </VListItem>
                <VListItem @click="resetPassword(item)">
                  重置密码
                </VListItem>
                <VListItem @click="deleteUser(item)">
                  删除
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="filteredUsers.length"
          />
        </template>
      </VDataTable>
    </VCard>

    <!-- 添加/编辑用户弹窗 -->
    <VNavigationDrawer
      v-if="dialogVisible"
      v-model="dialogVisible"
      location="right"
      temporary
      persistent
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ isEdit ? '编辑用户' : '添加用户' }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="dialogVisible = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 - flex-grow-1确保占据剩余空间 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar pt-4">
            <VForm @submit.prevent="saveUser">
              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  用户名
                </div>
                <AppTextField
                  v-model="editUser.name"
                  placeholder="请输入用户名"
                  :rules="[(v: string) => !!v || '请输入用户名']"
                />
              </div>

              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  邮箱
                </div>
                <AppTextField
                  v-model="editUser.email"
                  placeholder="请输入邮箱"
                  type="email"
                  :rules="[(v: string) => !!v || '请输入邮箱', (v: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) || '请输入有效的邮箱']"
                />
              </div>

              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  密码
                </div>
                <AppTextField
                  v-model="password"
                  placeholder="请输入密码"
                  :append-inner-icon="showPassword ? 'tabler-eye-off' : 'tabler-eye'"
                  :type="showPassword ? 'text' : 'password'"
                  :rules="[(v: string) => isEdit ? true : (!!v || '请输入密码'), (v: string) => isEdit ? true : (v.length >= 6 || '密码长度至少6位')]"
                  @click:append-inner="showPassword = !showPassword"
                />
              </div>

              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  角色
                </div>
                <AppSelect
                  v-model="editUser.roleId"
                  :items="roleOptions"
                  item-title="label"
                  item-value="value"
                  placeholder="请选择角色"
                  :rules="[(v: string) => !!v || '请选择角色']"
                />
              </div>

              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  状态
                </div>
                <div class="d-flex align-center">
                  <VSwitch
                    v-model="editUser.isActive"
                    class="mr-2"
                    :true-value="true"
                    :false-value="false"
                  />
                  <span class="text-subtitle-2 text-on-surface opacity-50">
                    {{ editUser.isActive === true ? '启用' : '禁用' }}
                  </span>
                </div>
              </div>
            </VForm>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="flex-shrink-0">
          <VDivider />
          <div class="pa-4 d-flex justify-end">
            <VBtn
              variant="tonal"
              color="secondary"
              class="mr-4"
              @click="dialogVisible = false"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="saveUser"
            >
              保存
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 批量分配角色弹窗 -->
    <VNavigationDrawer
      v-model="batchAssignRoleDialog"
      location="right"
      temporary
      persistent
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            批量分配角色
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="batchAssignRoleDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 - flex-grow-1确保占据剩余空间 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden pt-4">
          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <div class="d-flex justify-space-between align-start mb-4">
              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                选择角色
              </div>
              <AppSelect
                v-model="selectedRole"
                :items="roleOptions"
                item-title="label"
                item-value="value"
                placeholder="请选择角色"
                :rules="[(v: string) => !!v || '请选择角色']"
              />
            </div>

            <div class="mt-4">
              <p class="text-subtitle-2 text-on-surface opacity-70">
                已选择 {{ selectedRows.length }} 个用户
              </p>
            </div>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="flex-shrink-0">
          <VDivider />
          <div class="pa-4 d-flex justify-end">
            <VBtn
              variant="tonal"
              color="secondary"
              class="mr-4"
              @click="batchAssignRoleDialog = false"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="saveBatchAssignRole"
            >
              确定
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped>
.user-table td {
  white-space: nowrap;
}

.nowrap-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w-80px {
  inline-size: 80px;
}

.line-height-38px {
  line-height: 38px;
}
</style>
